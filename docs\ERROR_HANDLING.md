# Error Handling Implementation Guide

## Overview

This document explains the comprehensive error handling system implemented in the Gold Trading Platform. The system provides centralized error management, consistent API responses, proper logging, and graceful error recovery.

## Architecture Components

### 1. Logger Configuration (`config/logger.js`)

**Purpose**: Centralized logging with different levels and outputs

**Features**:
- Multiple log levels: error, warn, info, http, debug
- Console output for development
- File outputs for production (error.log, combined.log)
- Automatic log rotation (5MB max, 5 files)
- Exception and rejection handling

**Usage**:
```javascript
const logger = require('../config/logger');

logger.info('User logged in', { userId: 123 });
logger.error('Database error', { error: err.message });
```

### 2. API Helper (`helpers/api.helper.js`)

**Purpose**: Provides error handling utilities and response standardization

**Key Components**:

#### catchAsync Function
Wraps async route handlers to catch errors automatically:

```javascript
const { catchAsync } = require('../helpers/api.helper');

// Without transaction
exports.getUsers = catchAsync(async (req, res) => {
    const users = await User.findAll();
    return BaseController.sendResponse(res, users, 'Users fetched successfully');
});

// With transaction
exports.createUser = catchAsync(async (req, res) => {
    const user = await User.create(req.body, { transaction: req.transaction });
    return BaseController.sendResponse(res, user, 'User created successfully');
}, { useTransaction: true });
```

#### ApiError Class
Custom error class for operational errors:

```javascript
const { ApiError } = require('../helpers/api.helper');
const httpStatus = require('http-status');

// Throw custom errors
throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid input data');
throw new ApiError(httpStatus.UNAUTHORIZED, 'Access denied');
```

#### Response Functions
Standardized response methods:

```javascript
const { sendSuccess, sendError } = require('../helpers/api.helper');

// Success response
sendSuccess(res, userData, 'User created successfully', 201);

// Error response
sendError(res, 'User not found', 404);
```

### 3. Error Middleware (`middleware/error.middleware.js`)

**Purpose**: Centralized error processing and response handling

**Components**:

#### Error Converter
Converts different error types to ApiError instances:
- Sequelize validation errors → 400 Bad Request
- Unique constraint errors → 409 Conflict
- JWT errors → 401 Unauthorized
- Database errors → 500 Internal Server Error

#### Error Handler
Processes errors and sends appropriate responses:
- Logs all errors with context
- Hides internal errors in production
- Provides detailed error info in development

#### Usage in server.js:
```javascript
const { errorConverter, errorHandler, notFound } = require('./middleware/error.middleware');

// Handle unknown routes
app.use(notFound);

// Convert errors to ApiError
app.use(errorConverter);

// Handle all errors
app.use(errorHandler);
```

### 4. Enhanced BaseController (`helpers/BaseController.js`)

**Purpose**: Backward-compatible controller base class with new error handling

**New Methods**:
```javascript
// Async handler wrapper
static asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

// Action logging
static logAction(req, action, data = {}) {
    logger.info(`Controller Action: ${action}`, { userId: req.user?.user_id, ...data });
}

// Validation error handling
static sendValidationError(res, validationError, statusCode = 400) {
    return sendValidationError(res, validationError, statusCode);
}
```

### 5. Updated Authentication Middleware (`middleware/auth.middleware.js`)

**Purpose**: JWT authentication with proper error handling

**Features**:
- Uses ApiError for consistent error responses
- Proper JWT error handling
- Action logging for security audit
- Optional authentication support

## Implementation Examples

### 1. Basic Controller with Error Handling

```javascript
const { catchAsync, ApiError } = require('../helpers/api.helper');
const httpStatus = require('http-status');
const BaseController = require('../helpers/BaseController');

exports.getUser = catchAsync(async (req, res) => {
    const { userId } = req.params;
    
    const user = await User.findByPk(userId);
    
    if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
    }
    
    return BaseController.sendResponse(res, user, 'User fetched successfully');
});
```

### 2. Controller with Transaction

```javascript
exports.createUserWithWallet = catchAsync(async (req, res) => {
    const { name, email } = req.body;
    
    // Validation
    if (!name || !email) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Name and email are required');
    }
    
    // Create user (transaction handled by catchAsync)
    const user = await User.create({ name, email }, { transaction: req.transaction });
    
    // Create wallet
    const wallet = await Wallet.create({ 
        user_id: user.id, 
        balance: 0 
    }, { transaction: req.transaction });
    
    return BaseController.sendResponse(res, { user, wallet }, 'User created successfully');
}, { useTransaction: true });
```

### 3. Validation Error Handling

```javascript
exports.updateUser = catchAsync(async (req, res) => {
    // Joi validation
    const { error, value } = userUpdateSchema.validate(req.body);
    
    if (error) {
        throw new ApiError(httpStatus.BAD_REQUEST, error.details[0].message);
    }
    
    const user = await User.findByPk(req.params.userId);
    
    if (!user) {
        throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
    }
    
    await user.update(value);
    
    return BaseController.sendResponse(res, user, 'User updated successfully');
});
```

## Error Response Format

### Success Response
```json
{
    "success": true,
    "data": { ... },
    "message": "Operation successful"
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "data": [] // Optional error details (development only)
}
```

## Logging Structure

### Log Levels
- **error**: System errors, exceptions
- **warn**: Operational errors, failed validations
- **info**: General information, user actions
- **http**: HTTP requests/responses
- **debug**: Detailed debugging information

### Log Files
- `logs/error.log`: Error level logs only
- `logs/combined.log`: All log levels
- `logs/exceptions.log`: Uncaught exceptions
- `logs/rejections.log`: Unhandled promise rejections

## Best Practices

### 1. Use catchAsync for All Async Controllers
```javascript
// ✅ Good
exports.getUsers = catchAsync(async (req, res) => {
    // Controller logic
});

// ❌ Avoid
exports.getUsers = async (req, res) => {
    try {
        // Controller logic
    } catch (error) {
        // Manual error handling
    }
};
```

### 2. Throw ApiError for Operational Errors
```javascript
// ✅ Good
if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
}

// ❌ Avoid
if (!user) {
    return res.status(404).json({ error: 'User not found' });
}
```

### 3. Use Transactions for Multi-Step Operations
```javascript
// ✅ Good
exports.transferMoney = catchAsync(async (req, res) => {
    // All database operations use req.transaction automatically
    await fromWallet.update({ balance: newBalance }, { transaction: req.transaction });
    await toWallet.update({ balance: newBalance }, { transaction: req.transaction });
    
    return BaseController.sendResponse(res, result, 'Transfer successful');
}, { useTransaction: true });
```

### 4. Log Important Actions
```javascript
exports.deleteUser = catchAsync(async (req, res) => {
    BaseController.logAction(req, 'DELETE_USER_ATTEMPT', { userId: req.params.userId });
    
    // Delete logic
    
    BaseController.logAction(req, 'DELETE_USER_SUCCESS', { userId: req.params.userId });
});
```

## Migration Guide

### Updating Existing Controllers

1. **Add imports**:
```javascript
const { catchAsync, ApiError } = require('../helpers/api.helper');
const httpStatus = require('http-status');
```

2. **Wrap controller functions**:
```javascript
// Before
exports.getUser = async (req, res) => {
    try {
        // logic
    } catch (error) {
        // error handling
    }
};

// After
exports.getUser = catchAsync(async (req, res) => {
    // logic (no try-catch needed)
});
```

3. **Replace error responses**:
```javascript
// Before
return res.status(404).json({ error: 'Not found' });

// After
throw new ApiError(httpStatus.NOT_FOUND, 'Not found');
```

4. **Use transactions when needed**:
```javascript
// For operations that modify multiple tables
exports.complexOperation = catchAsync(async (req, res) => {
    // Use req.transaction for all database operations
}, { useTransaction: true });
```

This error handling system provides robust, scalable, and maintainable error management for the entire application.
