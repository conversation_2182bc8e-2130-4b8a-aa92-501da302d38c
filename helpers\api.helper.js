const logger = require('../config/logger');
const { sequelize } = require('../models');

/**
 * A helper to catch asynchronous errors and optionally manage a transaction.
 * 
 * @param {Function} fn - The async route handler (expects req, res, next).
 * @param {Object} [options] - Optional settings.
 * @param {boolean} [options.useTransaction=false] - If true, the handler is run in a new transaction.
 * @param {Function|null} [options.getTransaction=null] - Optional accessor function to retrieve an existing transaction from req.
 * @returns {Function} A function wrapping the original handler.
 */
const catchAsync = (fn, options = {}) => {
  const { useTransaction = false, getTransaction = null } = options;
  
  return async (req, res, next) => {
    try {
      if (!useTransaction) {
        // If transaction management is not desired, simply run the function.
        await fn(req, res, next);
        return;
      }

      // Otherwise, create a new transaction.
      const transaction = await sequelize.transaction();
      req.transaction = transaction;

      // Add user info to request body for audit trail
      if (req.user && req.body && ["POST", "PUT", "PATCH", "DELETE"].includes(req.method.toUpperCase())) {
        req.body.updated_by = req.user.user_id || req.user.id;
      }

      try {
        await fn(req, res, next);
        
        // Only commit if response hasn't been sent and no error occurred
        if (!res.headersSent) {
          await transaction.commit();
        }
      } catch (error) {
        // Rollback transaction on error
        await transaction.rollback();
        throw error; // Re-throw to be caught by outer catch
      }
    } catch (error) {
      // Handle transaction rollback for existing transactions
      if (getTransaction) {
        const existingTransaction = getTransaction(req);
        if (existingTransaction && typeof existingTransaction.rollback === 'function') {
          try {
            await existingTransaction.rollback();
            logger.info('Existing transaction rolled back due to error.');
          } catch (rollbackError) {
            logger.error('Failed to rollback existing transaction:', rollbackError);
          }
        }
      }
      
      // Pass error to error handling middleware
      next(error);
    }
  };
};

/**
 * Custom API Error class
 */
class ApiError extends Error {
  constructor(statusCode, message, isOperational = true, stack = '') {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Success Response Function
 * Maintains consistency with existing BaseController format
 */
const sendSuccess = (res, data = [], message = 'Success', statusCode = 200) => {
  const response = {
    success: true,
    data,
    message
  };

  return res.status(statusCode).json(response);
};

/**
 * Error Response Function
 * Maintains consistency with existing BaseController format
 */
const sendError = (res, message = 'Error', statusCode = 400, data = []) => {
  const response = {
    success: false,
    message
  };

  // Only add data field if there are error details
  if (data && data.length > 0) {
    response.data = data;
  }

  return res.status(statusCode).json(response);
};

/**
 * Validation Error Response Function
 * Specifically for Joi validation errors
 */
const sendValidationError = (res, error, statusCode = 400) => {
  const errorMessages = error.details.map(detail => ({
    field: detail.path.join('.'),
    message: detail.message
  }));

  return sendError(res, 'Validation failed', statusCode, errorMessages);
};

/**
 * Database Error Response Function
 * Specifically for Sequelize database errors
 */
const sendDatabaseError = (res, error, statusCode = 500) => {
  const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';
  
  let message = 'Database operation failed';
  let data = [];

  if (isDevelopment) {
    message = error.message;
    data = [{ error: error.stack }];
  }

  return sendError(res, message, statusCode, data);
};

module.exports = {
  ApiError,
  catchAsync,
  sendSuccess,
  sendError,
  sendValidationError,
  sendDatabaseError
};
