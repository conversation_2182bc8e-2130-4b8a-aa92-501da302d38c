const { Investment, User, Plan, Wallet, Bonus, Transaction } = require('../models');
const BaseController = require('../helpers/BaseController');
const { Op } = require('sequelize');
const { buyPlanSchema, getInvestmentSchema, getUserInvestmentsSchema } = require('../validations/investment.validation');
const { v4: uuidv4 } = require('uuid');


// Buy Plan - Main investment function
exports.buyPlan = async (req, res) => {
    const transaction = await Investment.sequelize.transaction();
    
    try {
        // Validate input
        const { error, value } = buyPlanSchema.validate(req.body, { abortEarly: false });
        
        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { plan_id, payment_method, transaction_id } = value;
        const user_id = req.user.id;

        // Check if user has any active investment
        const activeInvestment = await Investment.findOne({
            where: { 
                user_id,
                status: 'active'
            },
            transaction
        });

        if (activeInvestment) {
            await transaction.rollback();
            return BaseController.sendError(res, 'You already have an active investment. Please wait for it to complete before buying a new plan.');
        }

        // Get plan details
        const plan = await Plan.findOne({
            where: { 
                plan_id,
                is_active: true
            },
            transaction
        });

        if (!plan) {
            await transaction.rollback();
            return BaseController.sendError(res, 'Plan not found or inactive');
        }

        // Get user details with wallet
        const user = await User.findByPk(user_id, {
            include: [{
                model: Wallet,
                attributes: ['balance', 'bonus_balance']
            }],
            transaction
        });

        if (!user || !user.Wallet) {
            await transaction.rollback();
            return BaseController.sendError(res, 'User or wallet not found');
        }

        const investmentAmount = parseFloat(plan.investment_amount);
        const userBalance = parseFloat(user.Wallet.balance);
        const userBonusBalance = parseFloat(user.Wallet.bonus_balance);
        const totalBalance = userBalance + userBonusBalance;

        // Check wallet balance for wallet payment
        if (payment_method === 'wallet' && totalBalance < investmentAmount) {
            await transaction.rollback();
            return BaseController.sendError(res, `Insufficient balance. Required: ₹${investmentAmount}, Available: ₹${totalBalance.toFixed(2)}`);
        }

        // Calculate dates
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(startDate.getDate() + plan.duration);

        // Check if this is user's first investment
        const existingInvestments = await Investment.count({
            where: { user_id },
            transaction
        });
        const isFirstInvestment = existingInvestments === 0;

        // Calculate expected return
        const expectedReturn = investmentAmount + (investmentAmount * (parseFloat(plan.return_percentage) + parseFloat(plan.extra_return_percentage || 0)) / 100);

        // Create investment record
        const newInvestment = await Investment.create({
            user_id,
            plan_id,
            amount: investmentAmount,
            start_date: startDate,
            end_date: endDate,
            status: 'active',
            payment_method,
            transaction_id: transaction_id || uuidv4(),
            is_first_investment: isFirstInvestment,
            expected_return: expectedReturn
        }, { transaction });

        // Deduct amount from wallet if payment method is wallet
        if (payment_method === 'wallet') {
            let remainingAmount = investmentAmount;
            let newBalance = userBalance;
            let newBonusBalance = userBonusBalance;

            // First use regular balance
            if (remainingAmount > 0 && newBalance > 0) {
                const deductFromBalance = Math.min(remainingAmount, newBalance);
                newBalance -= deductFromBalance;
                remainingAmount -= deductFromBalance;
            }

            // Then use bonus balance if needed
            if (remainingAmount > 0 && newBonusBalance > 0) {
                const deductFromBonus = Math.min(remainingAmount, newBonusBalance);
                newBonusBalance -= deductFromBonus;
                remainingAmount -= deductFromBonus;
            }

            // Update wallet
            await user.Wallet.update({
                balance: newBalance,
                bonus_balance: newBonusBalance,
                total_invested: parseFloat(user.Wallet.total_invested || 0) + investmentAmount
            }, { transaction });
        }

        // Create investment transaction record
        await Transaction.create({
            user_id,
            transaction_type: 'investment',
            amount: investmentAmount,
            balance_type: 'main_balance',
            transaction_status: 'completed',
            description: `Investment in ${plan.name} plan`,
            reference_id: newInvestment.investment_id,
            reference_type: 'investment',
            payment_method,
            external_transaction_id: transaction_id || null,
            processed_at: new Date()
        }, { transaction });

        // Handle referral bonuses for first investment - ONLY referrer gets bonus
        if (isFirstInvestment && user.referred_by) {
            const referrer = await User.findByPk(user.referred_by, {
                include: [{ model: Wallet }],
                transaction
            });

            if (referrer && referrer.Wallet) {
                const bonusAmount = investmentAmount * 0.10; // 10% of investment amount

                // Create bonus transaction records (only for referrer)
                await Bonus.createReferralBonus(
                    user.referred_by,     // referrer user ID
                    user_id,              // referee user ID
                    newInvestment.investment_id,
                    bonusAmount,
                    transaction
                );

                // Give 10% bonus to referrer only
                await referrer.Wallet.update({
                    bonus_balance: parseFloat(referrer.Wallet.bonus_balance) + bonusAmount
                }, { transaction });

                // Create transaction record for referrer bonus
                await Transaction.create({
                    user_id: user.referred_by,
                    transaction_type: 'referral_bonus',
                    amount: bonusAmount,
                    balance_type: 'bonus_balance',
                    transaction_status: 'completed',
                    description: `Referral bonus for referring ${user.user_name} who made first investment`,
                    reference_id: newInvestment.investment_id,
                    reference_type: 'investment',
                    source_user_id: user_id,
                    processed_at: new Date()
                }, { transaction });

                // NOTE: New user (referee) no longer gets any bonus
            }
        }

        await transaction.commit();

        // Return success response
        return BaseController.sendResponse(res, {
            investment_id: newInvestment.investment_id,
            plan: {
                name: plan.name,
                amount: parseFloat(plan.amount),
                duration: plan.duration,
                return_percentage: parseFloat(plan.return_percentage),
                extra_return_percentage: parseFloat(plan.extra_return_percentage)
            },
            investment: {
                amount: parseFloat(newInvestment.amount),
                start_date: newInvestment.start_date,
                end_date: newInvestment.end_date,
                expected_return: parseFloat(newInvestment.expected_return),
                status: newInvestment.status,
                payment_method: newInvestment.payment_method,
                is_first_investment: newInvestment.is_first_investment
            }
        }, 'Plan purchased successfully');

    } catch (error) {
        await transaction.rollback();
        console.error('Buy plan error:', error);
        return BaseController.sendError(res, 'Failed to purchase plan', [{ error: error.message }]);
    }
};

// Get user investments
exports.getUserInvestments = async (req, res) => {
    try {
        const { error, value } = getUserInvestmentsSchema.validate(req.query, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { status, page, limit } = value;
        const user_id = req.user.id;
        const offset = (page - 1) * limit;

        // Build where clause
        const whereClause = { user_id };
        if (status !== 'all') {
            whereClause.status = status;
        }

        const investments = await Investment.findAndCountAll({
            where: whereClause,
            include: [{
                model: Plan,
                as: 'plan',
                attributes: ['name', 'description', 'return_percentage', 'extra_return_percentage']
            }],
            order: [['createdAt', 'DESC']],
            limit,
            offset
        });

        const formattedInvestments = investments.rows.map(investment => ({
            investment_id: investment.investment_id,
            plan: {
                name: investment.plan.name,
                description: investment.plan.description,
                return_percentage: parseFloat(investment.plan.return_percentage),
                extra_return_percentage: parseFloat(investment.plan.extra_return_percentage)
            },
            amount: parseFloat(investment.amount),
            expected_return: parseFloat(investment.expected_return),
            actual_return: investment.actual_return ? parseFloat(investment.actual_return) : null,
            start_date: investment.start_date,
            end_date: investment.end_date,
            status: investment.status,
            payment_method: investment.payment_method,
            is_first_investment: investment.is_first_investment,
            createdAt: investment.createdAt
        }));

        return BaseController.sendResponse(res, {
            investments: formattedInvestments,
            pagination: {
                current_page: page,
                total_pages: Math.ceil(investments.count / limit),
                total_records: investments.count,
                per_page: limit
            }
        }, 'Investments fetched successfully');

    } catch (error) {
        console.error('Get user investments error:', error);
        return BaseController.sendError(res, 'Failed to fetch investments', [{ error: error.message }]);
    }
};

// Get investment details
exports.getInvestmentDetails = async (req, res) => {
    try {
        const { error, value } = getInvestmentSchema.validate(req.params, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { investmentId } = value;
        const user_id = req.user.id;

        const investment = await Investment.findOne({
            where: {
                investment_id: investmentId,
                user_id
            },
            include: [{
                model: Plan,
                as: 'plan',
                attributes: ['name', 'description', 'amount', 'duration', 'return_percentage', 'extra_return_percentage']
            }]
        });

        if (!investment) {
            return BaseController.sendError(res, 'Investment not found');
        }

        return BaseController.sendResponse(res, {
            investment_id: investment.investment_id,
            plan: {
                name: investment.plan.name,
                description: investment.plan.description,
                amount: parseFloat(investment.plan.amount),
                duration: investment.plan.duration,
                return_percentage: parseFloat(investment.plan.return_percentage),
                extra_return_percentage: parseFloat(investment.plan.extra_return_percentage)
            },
            amount: parseFloat(investment.amount),
            expected_return: parseFloat(investment.expected_return),
            actual_return: investment.actual_return ? parseFloat(investment.actual_return) : null,
            start_date: investment.start_date,
            end_date: investment.end_date,
            status: investment.status,
            payment_method: investment.payment_method,
            transaction_id: investment.transaction_id,
            is_first_investment: investment.is_first_investment,
            createdAt: investment.createdAt,
            updatedAt: investment.updatedAt
        }, 'Investment details fetched successfully');

    } catch (error) {
        console.error('Get investment details error:', error);
        return BaseController.sendError(res, 'Failed to fetch investment details', [{ error: error.message }]);
    }
};


