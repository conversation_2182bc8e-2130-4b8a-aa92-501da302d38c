const { sendSuccess, sendError, sendValidationError, sendDatabaseError } = require('./api.helper');
const logger = require('../config/logger');

/**
 * Base Controller class with standardized response methods
 * Maintains backward compatibility while integrating with new error handling system
 */
class BaseController {
    /**
     * Send successful response
     * @param {Object} res - Express response object
     * @param {*} result - Data to send
     * @param {string} message - Success message
     * @param {number} statusCode - HTTP status code (default: 200)
     */
    static sendResponse(res, result, message = 'Success', statusCode = 200) {
        return sendSuccess(res, result, message, statusCode);
    }

    /**
     * Send error response (maintains backward compatibility)
     * @param {Object} res - Express response object
     * @param {string} error - Error message
     * @param {Array} errorMessages - Additional error details
     * @param {number} statusCode - HTTP status code (default: 400)
     */
    static sendError(res, error = 'Error', errorMessages = [], statusCode = 400) {
        // For backward compatibility, if errorMessages is a number, treat it as statusCode
        if (typeof errorMessages === 'number') {
            statusCode = errorMessages;
            errorMessages = [];
        }

        return sendError(res, error, statusCode, errorMessages);
    }

    /**
     * Send validation error response
     * @param {Object} res - Express response object
     * @param {Object} validationError - Joi validation error object
     * @param {number} statusCode - HTTP status code (default: 400)
     */
    static sendValidationError(res, validationError, statusCode = 400) {
        return sendValidationError(res, validationError, statusCode);
    }

    /**
     * Send database error response
     * @param {Object} res - Express response object
     * @param {Object} dbError - Database error object
     * @param {number} statusCode - HTTP status code (default: 500)
     */
    static sendDatabaseError(res, dbError, statusCode = 500) {
        return sendDatabaseError(res, dbError, statusCode);
    }

    /**
     * Handle async controller methods with automatic error handling
     * @param {Function} fn - Async controller function
     * @returns {Function} Wrapped controller function
     */
    static asyncHandler(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch(next);
        };
    }

    /**
     * Log controller actions for audit trail
     * @param {Object} req - Express request object
     * @param {string} action - Action being performed
     * @param {Object} data - Additional data to log
     */
    static logAction(req, action, data = {}) {
        logger.info(`Controller Action: ${action}`, {
            userId: req.user?.user_id || req.user?.id,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            method: req.method,
            url: req.originalUrl,
            ...data
        });
    }
}

module.exports = BaseController;
