const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const httpStatus = require('http-status');
const logger = require('./config/logger');
const db = require('./models');
const authRoutes = require('./routes/auth.route');
const userRoutes = require('./routes/user.routes');
const adminRoutes = require('./routes/admin.routes');
const planRoutes = require('./routes/plan.routes');
const investmentRoutes = require('./routes/investment.routes');
const bonusRoutes = require('./routes/bonus.routes');
const passwordResetRoutes = require('./routes/passwordReset.routes');
const { errorConverter, errorHandler, notFound, handleUncaughtException, handleUnhandledRejection } = require('./middleware/error.middleware');
const { ApiError } = require('./helpers/api.helper');

// 📦 Load environment variables from .env
dotenv.config();

// Handle uncaught exceptions and unhandled rejections
handleUncaughtException();
handleUnhandledRejection();

// 🚀 Initialize app
const app = express();

// 🛡️ Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // Enable form parsing

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();

  // Log request
  logger.http(`${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.user_id || req.user?.id
  });

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.http(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
  });

  next();
});

// 🔗 Routes
app.use('/api', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/plans', planRoutes);
app.use('/api/investments', investmentRoutes);
app.use('/api/bonuses', bonusRoutes);
app.use('/', passwordResetRoutes);

// 🧠 Default route
app.get('/', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Welcome to Gold Trading Platform API!',
        data: {
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            timestamp: new Date().toISOString()
        }
    });
});

// Health check route
app.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Server is healthy',
        data: {
            uptime: process.uptime(),
            timestamp: new Date().toISOString(),
            memory: process.memoryUsage()
        }
    });
});

// Handle unknown routes (404)
app.use(notFound);

// Convert errors to ApiError instances
app.use(errorConverter);

// Handle errors
app.use(errorHandler);

// ⚙️ Connect to DB and start server
const PORT = process.env.PORT || 3000;

// Test database connection first
db.sequelize.sync()
    .then(() => {
        logger.info('✅ Database connection established successfully');

        // Use force: false and alter: false to avoid index conflicts
        // For production, use migrations instead of sync
        return db.sequelize.sync({ force: false, alter: false });
    })
    .then(() => {
        logger.info('✅ Database synchronized');

        const server = app.listen(PORT, () => {
            logger.info(`🚀 Server running on http://localhost:${PORT}`);
            logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
        });

        // Graceful shutdown
        const gracefulShutdown = (signal) => {
            logger.info(`Received ${signal}. Shutting down gracefully...`);
            server.close(() => {
                logger.info('Server closed');
                db.sequelize.close()
                    .then(() => {
                        logger.info('Database connection closed');
                        process.exit(0);
                    })
                    .catch((err) => {
                        logger.error('Error closing database connection:', err);
                        process.exit(1);
                    });
            });
        };

        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    })
    .catch(err => {
        logger.error('❌ Failed to connect to DB:', {
            message: err.message,
            stack: err.stack
        });
        process.exit(1);
    });
