const jwt = require('jsonwebtoken');
const httpStatus = require('http-status');
const { ApiError } = require('../helpers/api.helper');
const logger = require('../config/logger');

/**
 * Verify JWT token and ensure user role
 */
exports.verifyToken = (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];

        // Check for Bearer token
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new ApiError(httpStatus.UNAUTHORIZED, 'Authorization token is missing or malformed');
        }

        const token = authHeader.split(' ')[1];

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has user role
        if (decoded.role !== 'user') {
            throw new ApiError(httpStatus.FORBIDDEN, 'Access denied. User role required.');
        }

        // Log successful authentication
        logger.info('User authenticated successfully', {
            userId: decoded.user_id || decoded.id,
            username: decoded.user_name,
            role: decoded.role,
            ip: req.ip
        });

        next();
    } catch (error) {
        // Handle JWT specific errors
        if (error.name === 'JsonWebTokenError') {
            return next(new ApiError(httpStatus.UNAUTHORIZED, 'Invalid token'));
        }
        if (error.name === 'TokenExpiredError') {
            return next(new ApiError(httpStatus.UNAUTHORIZED, 'Token expired'));
        }

        // Pass other errors to error handler
        next(error);
    }
};

/**
 * Verify JWT token and ensure admin role
 */
exports.verifyAdmin = (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];

        // Check for Bearer token
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new ApiError(httpStatus.UNAUTHORIZED, 'Authorization token is missing or malformed');
        }

        const token = authHeader.split(' ')[1];

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded; // { id: ..., user_id: ..., user_name: ..., email: ..., role: ... }

        // Check if user has admin role
        if (decoded.role !== 'admin') {
            throw new ApiError(httpStatus.FORBIDDEN, 'Access denied. Admin role required.');
        }

        // Log successful admin authentication
        logger.info('Admin authenticated successfully', {
            userId: decoded.user_id || decoded.id,
            username: decoded.user_name,
            role: decoded.role,
            ip: req.ip
        });

        next();
    } catch (error) {
        // Handle JWT specific errors
        if (error.name === 'JsonWebTokenError') {
            return next(new ApiError(httpStatus.UNAUTHORIZED, 'Invalid token'));
        }
        if (error.name === 'TokenExpiredError') {
            return next(new ApiError(httpStatus.UNAUTHORIZED, 'Token expired'));
        }

        // Pass other errors to error handler
        next(error);
    }
};

/**
 * Optional authentication - doesn't fail if no token provided
 * Useful for routes that work for both authenticated and unauthenticated users
 */
exports.optionalAuth = (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];

        // If no auth header, continue without user
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next();
        }

        const token = authHeader.split(' ')[1];

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'mysecretkey');
        req.user = decoded;

        logger.info('Optional auth successful', {
            userId: decoded.user_id || decoded.id,
            username: decoded.user_name,
            role: decoded.role
        });

        next();
    } catch (error) {
        // For optional auth, log the error but don't fail the request
        logger.warn('Optional auth failed', { error: error.message });
        next(); // Continue without user
    }
};
