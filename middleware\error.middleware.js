const {
  ValidationError,
  Database<PERSON>rror,
  ConnectionError,
  UniqueConstraintError,
  ForeignKeyConstraintError,
  TimeoutError
} = require("sequelize");
const httpStatus = require("http-status");
const logger = require("../config/logger");
const { sendError, ApiError } = require("../helpers/api.helper");

/**
 * Error converter middleware
 * Converts different types of errors to ApiError instances
 */
const errorConverter = (err, req, res, next) => {
  let error = err;
  
  if (!(error instanceof ApiError)) {
    let statusCode = httpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let isOperational = false;

    // Handle Sequelize Validation Errors
    if (error instanceof ValidationError) {
      statusCode = httpStatus.BAD_REQUEST;
      message = error.errors.map(e => e.message).join(', ');
      isOperational = true;
    }
    // Handle Sequelize Unique Constraint Errors
    else if (error instanceof UniqueConstraintError) {
      statusCode = httpStatus.CONFLICT;
      const field = error.errors[0]?.path || 'field';
      message = `${field} already exists`;
      isOperational = true;
    }
    // Handle Sequelize Foreign Key Constraint Errors
    else if (error instanceof ForeignKeyConstraintError) {
      statusCode = httpStatus.BAD_REQUEST;
      message = 'Invalid reference to related data';
      isOperational = true;
    }
    // Handle Sequelize Database Errors
    else if (error instanceof DatabaseError) {
      statusCode = httpStatus.INTERNAL_SERVER_ERROR;
      message = process.env.NODE_ENV === 'production' ? 'Database error occurred' : error.message;
      isOperational = false;
    }
    // Handle Sequelize Connection Errors
    else if (error instanceof ConnectionError) {
      statusCode = httpStatus.SERVICE_UNAVAILABLE;
      message = 'Database connection error';
      isOperational = false;
    }
    // Handle Sequelize Timeout Errors
    else if (error instanceof TimeoutError) {
      statusCode = httpStatus.REQUEST_TIMEOUT;
      message = 'Database operation timed out';
      isOperational = true;
    }
    // Handle JWT Errors
    else if (error.name === 'JsonWebTokenError') {
      statusCode = httpStatus.UNAUTHORIZED;
      message = 'Invalid token';
      isOperational = true;
    }
    else if (error.name === 'TokenExpiredError') {
      statusCode = httpStatus.UNAUTHORIZED;
      message = 'Token expired';
      isOperational = true;
    }
    // Handle Joi Validation Errors
    else if (error.isJoi) {
      statusCode = httpStatus.BAD_REQUEST;
      message = error.details[0].message;
      isOperational = true;
    }
    // Handle Multer Errors (file upload)
    else if (error.code === 'LIMIT_FILE_SIZE') {
      statusCode = httpStatus.BAD_REQUEST;
      message = 'File too large';
      isOperational = true;
    }
    else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      statusCode = httpStatus.BAD_REQUEST;
      message = 'Unexpected file field';
      isOperational = true;
    }
    // Handle other known errors
    else if (error.statusCode && error.message) {
      statusCode = error.statusCode;
      message = error.message;
      isOperational = true;
    }

    error = new ApiError(statusCode, message, isOperational, err.stack);
  }
  
  next(error);
};

/**
 * Error handler middleware
 * Sends error response to client and logs errors
 */
const errorHandler = (err, req, res, next) => {
  let { statusCode, message, isOperational } = err;
  
  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && !isOperational) {
    statusCode = httpStatus.INTERNAL_SERVER_ERROR;
    message = 'Something went wrong';
  }

  // Set error message in response locals for logging
  res.locals.errorMessage = err.message;

  // Prepare error data for development/local environments
  let errorData = [];
  const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local';
  
  if (isDevelopment) {
    errorData = [{
      stack: err.stack,
      originalError: err.name,
      isOperational: err.isOperational
    }];
  }

  // Log error details
  const logLevel = err.isOperational ? 'warn' : 'error';
  logger[logLevel](`${req.method} ${req.originalUrl} - ${statusCode} - ${message}`, {
    error: err.message,
    stack: err.stack,
    userId: req.user?.user_id || req.user?.id,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Send error response
  return sendError(res, message, statusCode, errorData);
};

/**
 * Handle 404 errors for unknown routes
 */
const notFound = (req, res, next) => {
  const error = new ApiError(httpStatus.NOT_FOUND, `Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Handle uncaught exceptions
 */
const handleUncaughtException = () => {
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
  });
};

/**
 * Handle unhandled promise rejections
 */
const handleUnhandledRejection = () => {
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
  });
};

module.exports = {
  errorConverter,
  errorHandler,
  notFound,
  handleUncaughtException,
  handleUnhandledRejection
};
